All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html),
and is generated by [<PERSON><PERSON>](https://github.com/miniscruff/changie).


## 1.0.0 - 2025-02-26
### Added
* Introduces hub.continue.dev
* Improved theme matching
### Fixed
* Fixes interference between SonarQube and Continue autocomplete
* Numerous reliability improvements

## 0.0.92 - 2025-02-26
### Fixed
* Off-screen rendering to solve white flash on load and lack of changing cursor type
* OSR-related fixes for non-Mac users
* Fixes for inline edit in JetBrains

## 0.0.72 - 2024-10-04
### Added
* Listen for changes to Intellij settings without requiring window reload
### Changed
* Updated tutorial file
### Fixed
* Fix ability to load config.ts in JetBrains IDEs

## 0.0.69 - 2024-09-22
### Added
* Support for the "search" context provider
### Fixed
* Fixed bug where only first module would be recognized
* Improved concurrency handling to avoid freezes
* Made compatible with latest JetBrains versions

## 0.0.54 - 2024-07-13
### Added
* Partial autocomplete acceptance
* Autocomplete status bar spinner
### Fixed
* Fixed duplicate completion bug and others

## 0.0.53 - 2024-07-10
### Added
* Support for .prompt files
* New onboarding experience
### Fixed
* Indexing fixes from VS Code versions merged into IntelliJ
* Improved codebase indexing reliability and testing
* Fixes for autocomplete text positioning and timing

## 0.0.42 - 2024-04-12
### Added
* Inline cmd/ctrl+I in JetBrains
### Fixed
* Fixed character encoding error causing display issues
* Fixed error causing input to constantly demand focus
* Fixed automatic reloading of config.json

## 0.0.38 - 2024-03-15
### Added
* Remote config server support
* Autocomplete support in JetBrains

## 0.0.34 - 2024-03-03
### Added
* diff context provider
### Changed
* Allow LLM servers to handle templating
### Fixed
* Fix a few context providers / slash commands
* Fixed issues preventing proper extension startup

## v0.0.26 - 2023-12-28
### Added
* auto-reloading of config on save
### Fixed
* Fixed /edit bug for versions without Python server

## v0.0.25 - 2023-12-25

### Changed

- Intellij extension no longer relies on the Continue Python server

## v0.0.21 - 2023-12-05

### Added

- updated to match latest VS Code updates

## v0.0.19 - 2023-11-19

### Changed

- migrated to .json config file format

## v0.0.1 - 2023-09-01

### Added

- Initial scaffold created from [IntelliJ Platform Plugin Template](https://github.com/JetBrains/intellij-platform-plugin-template)
