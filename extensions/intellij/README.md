<!-- Plugin description -->

<h1 align="center">Continue</h1>

<div align="center">

[**Continue**](https://docs.continue.dev) enables developers to create, share, and use custom AI code assistants with our open-source [JetBrains](https://plugins.jetbrains.com/plugin/22707-continue-extension) extension and [hub of models, rules, prompts, docs, and other building blocks](https://hub.continue.dev).

</div>

<div align="center">

## Chat

[Chat](https://continue.dev/docs/chat/how-to-use-it) makes it easy to ask for help from an LLM without needing to leave the IDE.

You send it a task, including any relevant information, and it replies with the text / code most likely to complete the task. If it does not give you what you want, then you can send follow up messages to clarify and adjust its approach until the task is completed.

## Autocomplete

[Autocomplete](https://continue.dev/docs/autocomplete/how-to-use-it) provides inline code suggestions as you type.

To enable it, simply click the "Continue" button in the status bar at the bottom right of your IDE or ensure the "Enable Tab Autocomplete" option is checked in your IDE settings.

## Edit

[Edit](https://continue.dev/docs/edit/how-to-use-it) is a convenient way to modify code without leaving your current file.

Highlight a block of code, describe your code changes, and a diff will be streamed inline to your file which you can accept or reject.

## Actions

[Actions](https://continue.dev/docs/actions/how-to-use-it) are shortcuts for common use cases.

For example, you might want to review code, write tests, or add a docstring.

</div>

## License

[Apache 2.0 © 2023-2024 Continue Dev, Inc.](./LICENSE)

<!-- Plugin description end -->
