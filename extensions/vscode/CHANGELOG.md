All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html),
and is generated by [<PERSON><PERSON>](https://github.com/miniscruff/changie).


## 1.0.0 - 2025-02-26
### Added
* Introduces hub.continue.dev
* Improved apply quality
### Fixed
* Numerous reliability improvements

## 0.8.66 - 2024-12-20
### Changed
* Improved autocomplete in untitled files
### Fixed
* Display more mid-line completions
* Restored syntax highlighting
* Fix tool use bug for models that don't support tools
* Autodetect mistral API key type
* Fixes Azure OpenAI regressions

## 0.8.62 - 2024-12-10
### Added
* Tool use
* Support for Model Context Protocol
### Fixed
* hotfix: context providers no longer hidden when not in edit mode

## 0.8.59 - 2024-11-25
### Fixed
* Hotfix for Ollama onboarding

## 0.8.58 - 2024-11-22
### Added
* OpenAI predicted outputs support
* Improve codebase retrieval with BM25
* Support for Grok from xAI
* Chat enhancements including sticking input to bottom
* New UI for cmd+I in sidebar
* Support for Nebius LLM provider
* Support for Ask Sage LLM provider
* Improved reference for config.json
* New @web context provider
* Updates for llama3.2
* .continuerules file to set system prompt
* .prompt files v2
* Dedicated UI for docs indexing
* Clickable code symbols in chat
* Use clipboard content as autocomplete context
### Changed
* Improved @docs crawler
* Many improvements to make autocomplete more eager
* Near complete type definition retrieval for TypeScript autocomplete
* Remove footer from chat sidebar
### Fixed
* Brought back the Apply button for all code blocks
* Automatically update codebase index on removed files

## 0.8.56 - 2024-11-08
### Added
* New Edit mode in sidebar (cmd/ctrl+I)
* Significantly faster and more accurate docs crawler by default

## 0.8.55 - 2024-10-25
### Added
* Web context provider
* Cerebras inference provider
* Automatic descriptions for previous chats
* Discord context provider
* Improved full screen UI
### Changed
* Easier way to accept/reject/re-prompt after cmd/ctrl+I

## 0.8.54 - 2024-10-15
### Fixed
* Hotfix: throttle transformers.js embeddings provider

## 0.8.53 - 2024-10-11
### Added
* Improved loading/accept/reject UI for apply
* Polished chat sidebar, especially context dropdown
* Further prompt caching with Anthropic
### Changed
* Updated tutorial file
* Improved styling on "More" page
### Fixed
* Continue for teams auth bug fix
* Fixed a number of apply bugs
* Fixed autoscrolling behavior

## 0.8.52 - 2024-09-16
### Changed
* Use Chromium only as a fallback after asking user
* Redesigned onboarding flow

## 0.8.51 - 2024-09-05
### Fixed
* Fixed CRLF bug causing diff streams to treat every line as changed on Windows

## 0.8.50 - 2024-09-02
### Fixed
* Hotfix for ability to use more than one inline context provider

## 0.8.49 - 2024-09-01
### Fixed
* Hotfix: submenu context providers

## 0.8.48 - 2024-09-01
### Added
* Improved indexing progress UI
* Improved @codebase using repomap
* Repo map context provider
### Fixed
* Many small UI improvements
* Fixes db.search not a function

## 0.8.47 - 2024-08-27
### Added
* Use headless browser for crawling to get better results
* TTS support in the chat window
### Changed
* Improved support for WatsonX models
### Fixed
* Fixed several small indexing bugs

## 0.8.46 - 2024-08-11
### Added
* new /onboard slash command
### Fixed
* Fixed problem loading config.ts
* Fixed bug causing duplicate indexing work

## 0.8.45 - 2024-08-05
### Added
* Support for Llama 3.1 and gpt-4o-mini
* Support for WatsonX+Granite models
### Changed
* Significant improvements to indexing performance
* Improved @codebase quality by more accurately searching over file names and paths
* Improved @codebase accuracy
* Further improvements to indexing performance
### Fixed
* Improved docs indexing and management
* Fixed Gemini embeddings provider

## 0.8.43 - 2024-07-08
### Added
* Improved indexing reliability and testing
* Quick Actions: use CodeLens to quickly take common actions like adding docstrings

## 0.8.42 - 2024-07-02

### Added

- Support for Gemini 1.5 Pro
- Link to code in the sidebar when using codebase retrieval
- Smoother onboarding experience
- .prompt files, a way of saving and sharing slash commands
- Support for Claude 3.5 Sonnet, Deepseek Coder v2, and other new models
- Support for comments in config.json
- Specify multiple autocomplete models and switch between them
- Improved bracket matching strategy reduces noisy completions

### Fixed

- Numerous reliability upgrades to codebase indexing

## 0.8.24 - 2024-04-12
### Added
* Support for improved retrieval models (Voyage embeddings/reranking)
* New @code context provider
* Personal usage analytics

## 0.8.15 - 2024-03-05
### Added
* Tab-autocomplete in beta

## 0.8.14 - 2024-03-03
### Added
* Image support
* Full-text search index for retrieval
* Docs context provider
* CodeLlama-70b support
### Changed
* config.ts only runs in NodeJS, not browser
### Fixed
* Fixed proxy setting in config.json

## v0.8.2 - 2024-01-23
### Added
* Add codellama and gemini to free trial, using new server
* Local codebase syncing and embeddings using LanceDB
* Improved VS Code theme matching
### Changed
* Updates to packaging to download native modules for current platform (lancedb, sqlite, onnxruntime, tree-sitter wasms)
* Context providers now run from the extension side (in Node.js instead of browser javascript)

## v0.8.1 - 2024-01-08

### Added

- disableSessionTitles option in config.json

### Changed

- Use Ollama /chat endpoint instead of raw completions by default, and /show endpoint to gather model parameters like context length and stop tokens

## v0.6.19 - 2024-01-05

### Added

- support for .continuerc.json in root of workspace to override config.json
- Inline context providers
- cmd+shift+L with new diff streaming UI for edits

### Changed

- Allow certain LLM servers to handle templating

## v0.6.16 - 2023-12-25

### Changed

- Context items are now kept around as a part of past messages, instead of staying at the main input
- No more Python server - Continue runs entirely in Typescript

## v0.6.4 - 2023-11-19

### Changed

- migrated to .json config file format

## v0.6.0 - 2023-11-10

### Added

- Full screen mode
- StackOverflow slash command to augment with web search
- VS Code context menus: right click to add code to context, debug the terminal, or share your Continue session

### Fixed

- Reliability improvements to JetBrains by bringing up-to-date with the socket.io refactor

## v0.5.0 - 2023-11-09

### Added

- Codebase Retrieval: Use /codebase or cmd+enter and Continue will automatically gather the most important context

### Changed

- Switch from Websockets to Socket.io
