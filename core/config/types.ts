const Types = `
declare global {
  import Parser from "web-tree-sitter";
  import { GetGhTokenArgs } from "./protocol/ide";
  declare global {
    interface Window {
      ide?: "vscode";
      windowId: string;
      serverUrl: string;
      vscMachineId: string;
      vscMediaUrl: string;
      fullColorTheme?: {
        rules?: {
          token?: string;
          foreground?: string;
        }[];
      };
      colorThemeName?: string;
      workspacePaths?: string[];
      postIntellijMessage?: (
        messageType: string,
        data: any,
        messageIde: string,
      ) => void;
    }
  }
  
  export interface ChunkWithoutID {
    content: string;
    startLine: number;
    endLine: number;
    signature?: string;
    otherMetadata?: { [key: string]: any };
  }
  
  export interface Chunk extends ChunkWithoutID {
    digest: string;
    filepath: string;
    index: number; // Index of the chunk in the document at filepath
  }
  
  export interface IndexingProgressUpdate {
    progress: number;
    desc: string;
    shouldClearIndexes?: boolean;
    status:
      | "loading"
      | "indexing"
      | "done"
      | "failed"
      | "paused"
      | "disabled"
      | "cancelled";
    debugInfo?: string;
  }
  
  // This is more or less a V2 of IndexingProgressUpdate for docs etc.
  export interface IndexingStatus {
    id: string;
    type: "docs";
    progress: number;
    description: string;
    status: "indexing" | "complete" | "paused" | "failed" | "aborted" | "pending";
    embeddingsProviderId: string;
    isReindexing?: boolean;
    debugInfo?: string;
    title: string;
    icon?: string;
    url?: string;
  }
  
  export type PromptTemplateFunction = (
    history: ChatMessage[],
    otherData: Record<string, string>,
  ) => string | ChatMessage[];
  
  export type PromptTemplate = string | PromptTemplateFunction;
  
  export interface ILLM extends LLMOptions {
    get providerName(): string;
  
    uniqueId: string;
    model: string;
  
    title?: string;
    systemMessage?: string;
    contextLength: number;
    maxStopWords?: number;
    completionOptions: CompletionOptions;
    requestOptions?: RequestOptions;
    promptTemplates?: Record<string, PromptTemplate>;
    templateMessages?: (messages: ChatMessage[]) => string;
    writeLog?: (str: string) => Promise<void>;
    llmRequestHook?: (model: string, prompt: string) => any;
    apiKey?: string;
    apiBase?: string;
    cacheBehavior?: CacheBehavior;
  
    deployment?: string;
    apiVersion?: string;
    apiType?: string;
    region?: string;
    projectId?: string;
  
    // Embedding options
    embeddingId: string;
    maxEmbeddingChunkSize: number;
    maxEmbeddingBatchSize: number;
  
    complete(
      prompt: string,
      signal: AbortSignal,
      options?: LLMFullCompletionOptions,
    ): Promise<string>;
  
    streamComplete(
      prompt: string,
      signal: AbortSignal,
      options?: LLMFullCompletionOptions,
    ): AsyncGenerator<string, PromptLog>;
  
    streamFim(
      prefix: string,
      suffix: string,
      signal: AbortSignal,
      options?: LLMFullCompletionOptions,
    ): AsyncGenerator<string, PromptLog>;
  
    streamChat(
      messages: ChatMessage[],
      signal: AbortSignal,
      options?: LLMFullCompletionOptions,
    ): AsyncGenerator<ChatMessage, PromptLog>;
  
    chat(
      messages: ChatMessage[],
      signal: AbortSignal,
      options?: LLMFullCompletionOptions,
    ): Promise<ChatMessage>;
  
    embed(chunks: string[]): Promise<number[][]>;
  
    rerank(query: string, chunks: Chunk[]): Promise<number[]>;
  
    countTokens(text: string): number;
  
    supportsImages(): boolean;
  
    supportsCompletions(): boolean;
  
    supportsPrefill(): boolean;
  
    supportsFim(): boolean;
  
    listModels(): Promise<string[]>;
  
    renderPromptTemplate(
      template: PromptTemplate,
      history: ChatMessage[],
      otherData: Record<string, string>,
      canPutWordsInModelsMouth?: boolean,
    ): string | ChatMessage[];
  }
  
  export type ContextProviderType = "normal" | "query" | "submenu";
  
  export interface ContextProviderDescription {
    title: ContextProviderName;
    displayTitle: string;
    description: string;
    renderInlineAs?: string;
    type: ContextProviderType;
    dependsOnIndexing?: boolean;
  }
  
  export type FetchFunction = (url: string | URL, init?: any) => Promise<any>;
  
  export interface ContextProviderExtras {
    config: ContinueConfig;
    fullInput: string;
    embeddingsProvider: ILLM;
    reranker: ILLM | undefined;
    llm: ILLM;
    ide: IDE;
    selectedCode: RangeInFile[];
    fetch: FetchFunction;
  }
  
  export interface LoadSubmenuItemsArgs {
    config: ContinueConfig;
    ide: IDE;
    fetch: FetchFunction;
  }
  
  export interface CustomContextProvider {
    title: string;
    displayTitle?: string;
    description?: string;
    renderInlineAs?: string;
    type?: ContextProviderType;
  
    getContextItems(
      query: string,
      extras: ContextProviderExtras,
    ): Promise<ContextItem[]>;
  
    loadSubmenuItems?: (
      args: LoadSubmenuItemsArgs,
    ) => Promise<ContextSubmenuItem[]>;
  }
  
  export interface ContextSubmenuItem {
    id: string;
    title: string;
    description: string;
    icon?: string;
    metadata?: any;
  }
  
  export interface SiteIndexingConfig {
    title: string;
    startUrl: string;
    maxDepth?: number;
    faviconUrl?: string;
  }
  
  export interface IContextProvider {
    get description(): ContextProviderDescription;
  
    getContextItems(
      query: string,
      extras: ContextProviderExtras,
    ): Promise<ContextItem[]>;
  
    loadSubmenuItems(args: LoadSubmenuItemsArgs): Promise<ContextSubmenuItem[]>;
  }
  
  export interface Checkpoint {
    [filepath: string]: string;
  }
  
  export interface Session {
    sessionId: string;
    title: string;
    workspaceDirectory: string;
    history: ChatHistoryItem[];
  }
  
  export interface SessionMetadata {
    sessionId: string;
    title: string;
    dateCreated: string;
    workspaceDirectory: string;
  }
  
  export interface RangeInFile {
    filepath: string;
    range: Range;
  }
  
  export interface Location {
    filepath: string;
    position: Position;
  }
  
  export interface FileWithContents {
    filepath: string;
    contents: string;
  }
  
  export interface Range {
    start: Position;
    end: Position;
  }
  
  export interface Position {
    line: number;
    character: number;
  }
  
  export interface FileEdit {
    filepath: string;
    range: Range;
    replacement: string;
  }
  
  export interface ContinueError {
    title: string;
    message: string;
  }
  
  export interface CompletionOptions extends BaseCompletionOptions {
    model: string;
  }
  
  export type ChatMessageRole = "user" | "assistant" | "system" | "tool";
  
  export interface MessagePart {
    type: "text" | "imageUrl";
    text?: string;
    imageUrl?: { url: string };
  }
  
  export type MessageContent = string | MessagePart[];
  
  export interface ToolCall {
    id: string;
    type: "function";
    function: {
      name: string;
      arguments: string;
    };
  }
  
  export interface ToolCallDelta {
    id?: string;
    type?: "function";
    function?: {
      name?: string;
      arguments?: string;
    };
  }
  
  export interface ToolResultChatMessage {
    role: "tool";
    content: string;
    toolCallId: string;
  }
  
  export interface UserChatMessage {
    role: "user";
    content: MessageContent;
  }
  
  export interface AssistantChatMessage {
    role: "assistant";
    content: MessageContent;
    toolCalls?: ToolCallDelta[];
  }
  
  export interface SystemChatMessage {
    role: "system";
    content: string;
  }
  
  export type ChatMessage =
    | UserChatMessage
    | AssistantChatMessage
    | SystemChatMessage
    | ToolResultChatMessage;
  
  export interface ContextItemId {
    providerTitle: string;
    itemId: string;
  }
  
  export type ContextItemUriTypes = "file" | "url";
  
  export interface ContextItemUri {
    type: ContextItemUriTypes;
    value: string;
  }
  
  export interface ContextItem {
    content: string;
    name: string;
    description: string;
    editing?: boolean;
    editable?: boolean;
    icon?: string;
    uri?: ContextItemUri;
    hidden?: boolean;
  }
  
  export interface ContextItemWithId extends ContextItem {
    id: ContextItemId;
  }
  
  export interface InputModifiers {
    useCodebase: boolean;
    noContext: boolean;
  }
  
  export interface SymbolWithRange extends RangeInFile {
    name: string;
    type: Parser.SyntaxNode["type"];
    content: string;
  }
  
  export type FileSymbolMap = Record<string, SymbolWithRange[]>;
  
  export interface PromptLog {
    modelTitle: string;
    completionOptions: CompletionOptions;
    prompt: string;
    completion: string;
  }
  
  type MessageModes = "chat" | "edit";
  
  export type ToolStatus =
    | "generating"
    | "generated"
    | "calling"
    | "done"
    | "canceled";
  
  // Will exist only on "assistant" messages with tool calls
  interface ToolCallState {
    toolCallId: string;
    toolCall: ToolCall;
    status: ToolStatus;
    parsedArgs: any;
    output?: ContextItem[];
  }
  
  export interface ChatHistoryItem {
    message: ChatMessage;
    contextItems: ContextItemWithId[];
    editorState?: any;
    modifiers?: InputModifiers;
    promptLogs?: PromptLog[];
    toolCallState?: ToolCallState;
    isGatheringContext?: boolean;
    checkpoint?: Checkpoint;
    isBeforeCheckpoint?: boolean;
  }
  
  export interface LLMFullCompletionOptions extends BaseCompletionOptions {
    log?: boolean;
    model?: string;
  }
  
  export type ToastType = "info" | "error" | "warning";
  
  export interface LLMOptions {
    model: string;
  
    title?: string;
    uniqueId?: string;
    systemMessage?: string;
    contextLength?: number;
    maxStopWords?: number;
    completionOptions?: CompletionOptions;
    requestOptions?: RequestOptions;
    template?: TemplateType;
    promptTemplates?: Record<string, PromptTemplate>;
    templateMessages?: (messages: ChatMessage[]) => string;
    writeLog?: (str: string) => Promise<void>;
    llmRequestHook?: (model: string, prompt: string) => any;
    apiKey?: string;
    aiGatewaySlug?: string;
    apiBase?: string;
    cacheBehavior?: CacheBehavior;
  
    useLegacyCompletionsEndpoint?: boolean;
  
    // Embedding options
    embeddingId?: string;
    maxEmbeddingChunkSize?: number;
    maxEmbeddingBatchSize?: number;
  
    // Cloudflare options
    accountId?: string;
  
    // Azure options
    deployment?: string;
    apiVersion?: string;
    apiType?: string;
  
    // AWS options
    profile?: string;
    modelArn?: string;
  
    // AWS and GCP Options
    region?: string;
  
    // GCP Options
    capabilities?: ModelCapability;
  
    // GCP and Watsonx Options
    projectId?: string;
  
    // IBM watsonx Options
    deploymentId?: string;
  }
  
  type RequireAtLeastOne<T, Keys extends keyof T = keyof T> = Pick<
    T,
    Exclude<keyof T, Keys>
  > &
    {
      [K in Keys]-?: Required<Pick<T, K>> & Partial<Pick<T, Exclude<Keys, K>>>;
    }[Keys];
  
  export interface CustomLLMWithOptionals {
    options: LLMOptions;
    streamCompletion?: (
      prompt: string,
      signal: AbortSignal,
      options: CompletionOptions,
      fetch: (input: RequestInfo | URL, init?: RequestInit) => Promise<Response>,
    ) => AsyncGenerator<string>;
    streamChat?: (
      messages: ChatMessage[],
      signal: AbortSignal,
      options: CompletionOptions,
      fetch: (input: RequestInfo | URL, init?: RequestInit) => Promise<Response>,
    ) => AsyncGenerator<string>;
    listModels?: (
      fetch: (input: RequestInfo | URL, init?: RequestInit) => Promise<Response>,
    ) => Promise<string[]>;
  }
  
  /**
   * The LLM interface requires you to specify either \`streamCompletion\` or \`streamChat\` (or both).
   */
  export type CustomLLM = RequireAtLeastOne<
    CustomLLMWithOptionals,
    "streamCompletion" | "streamChat"
  >;
  
  // IDE
  
  export type DiffLineType = "new" | "old" | "same";
  
  export interface DiffLine {
    type: DiffLineType;
    line: string;
  }
  
  export class Problem {
    filepath: string;
    range: Range;
    message: string;
  }
  
  export class Thread {
    name: string;
    id: number;
  }
  
  export type IdeType = "vscode" | "jetbrains";
  
  export interface IdeInfo {
    ideType: IdeType;
    name: string;
    version: string;
    remoteName: string;
    extensionVersion: string;
  }
  
  export interface BranchAndDir {
    branch: string;
    directory: string;
  }
  
  export interface IndexTag extends BranchAndDir {
    artifactId: string;
  }
  
  export enum FileType {
    Unkown = 0,
    File = 1,
    Directory = 2,
    SymbolicLink = 64,
  }
  
  export interface IdeSettings {
    remoteConfigServerUrl: string | undefined;
    remoteConfigSyncPeriod: number;
    userToken: string;
    enableControlServerBeta: boolean;
    pauseCodebaseIndexOnStart: boolean;
  }
  
  export interface IDE {
    getIdeInfo(): Promise<IdeInfo>;
  
    getIdeSettings(): Promise<IdeSettings>;
  
    getDiff(includeUnstaged: boolean): Promise<string[]>;
  
    getClipboardContent(): Promise<{ text: string; copiedAt: string }>;
  
    isTelemetryEnabled(): Promise<boolean>;
  
    getUniqueId(): Promise<string>;
  
    getTerminalContents(): Promise<string>;
  
    getDebugLocals(threadIndex: number): Promise<string>;
  
    getTopLevelCallStackSources(
      threadIndex: number,
      stackDepth: number,
    ): Promise<string[]>;
  
    getAvailableThreads(): Promise<Thread[]>;
  
    getWorkspaceDirs(): Promise<string[]>;
  
    getWorkspaceConfigs(): Promise<ContinueRcJson[]>;
  
    fileExists(filepath: string): Promise<boolean>;
  
    writeFile(path: string, contents: string): Promise<void>;
  
    showVirtualFile(title: string, contents: string): Promise<void>;
    openFile(path: string): Promise<void>;
  
    openUrl(url: string): Promise<void>;
  
    runCommand(command: string): Promise<void>;
  
    saveFile(filepath: string): Promise<void>;
  
    readFile(filepath: string): Promise<string>;
  
    readRangeInFile(filepath: string, range: Range): Promise<string>;
  
    showLines(
      filepath: string,
      startLine: number,
      endLine: number,
    ): Promise<void>;
    getOpenFiles(): Promise<string[]>;
  
    getCurrentFile(): Promise<
      | undefined
      | {
          isUntitled: boolean;
          path: string;
          contents: string;
        }
    >;
  
    getPinnedFiles(): Promise<string[]>;
  
    getSearchResults(query: string): Promise<string>;
  
    subprocess(command: string, cwd?: string): Promise<[string, string]>;
  
    getProblems(filepath?: string | undefined): Promise<Problem[]>;
  
    getBranch(dir: string): Promise<string>;
  
    getTags(artifactId: string): Promise<IndexTag[]>;
  
    getRepoName(dir: string): Promise<string | undefined>;
  
    showToast(
      type: ToastType,
      message: string,
      ...otherParams: any[]
    ): Promise<any>;
  
    getGitRootPath(dir: string): Promise<string | undefined>;
  
    listDir(dir: string): Promise<[string, FileType][]>;
  
    getLastModified(files: string[]): Promise<{ [path: string]: number }>;
  
    getGitHubAuthToken(args: GetGhTokenArgs): Promise<string | undefined>;
  
    // LSP
    gotoDefinition(location: Location): Promise<RangeInFile[]>;
  
    // Callbacks
    onDidChangeActiveTextEditor(callback: (filepath: string) => void): void;
  }
  
  // Slash Commands
  
  export interface ContinueSDK {
    ide: IDE;
    llm: ILLM;
    addContextItem: (item: ContextItemWithId) => void;
    history: ChatMessage[];
    input: string;
    params?: { [key: string]: any } | undefined;
    contextItems: ContextItemWithId[];
    selectedCode: RangeInFile[];
    config: ContinueConfig;
    fetch: FetchFunction;
  }
  
  export interface SlashCommand {
    name: string;
    description: string;
    params?: { [key: string]: any };
    run: (sdk: ContinueSDK) => AsyncGenerator<string | undefined>;
  }
  
  // Config
  
  type StepName =
    | "AnswerQuestionChroma"
    | "GenerateShellCommandStep"
    | "EditHighlightedCodeStep"
    | "ShareSessionStep"
    | "CommentCodeStep"
    | "ClearHistoryStep"
    | "StackOverflowStep"
    | "OpenConfigStep"
    | "GenerateShellCommandStep"
    | "DraftIssueStep";
  
  type ContextProviderName =
    | "diff"
    | "terminal"
    | "debugger"
    | "open"
    | "google"
    | "search"
    | "tree"
    | "http"
    | "codebase"
    | "problems"
    | "folder"
    | "jira"
    | "postgres"
    | "database"
    | "code"
    | "docs"
    | "gitlab-mr"
    | "os"
    | "currentFile"
    | "greptile"
    | "outline"
    | "continue-proxy"
    | "highlights"
    | "file"
    | "issue"
    | "repo-map"
    | "url"
    | string;
  
  type TemplateType =
    | "llama2"
    | "alpaca"
    | "zephyr"
    | "phi2"
    | "phind"
    | "anthropic"
    | "chatml"
    | "none"
    | "openchat"
    | "deepseek"
    | "xwin-coder"
    | "neural-chat"
    | "codellama-70b"
    | "llava"
    | "gemma"
    | "granite"
    | "llama3";
  
  export interface RequestOptions {
    timeout?: number;
    verifySsl?: boolean;
    caBundlePath?: string | string[];
    proxy?: string;
    headers?: { [key: string]: string };
    extraBodyProperties?: { [key: string]: any };
    noProxy?: string[];
    clientCertificate?: ClientCertificateOptions;
  }
  
  export interface CacheBehavior {
    cacheSystemMessage?: boolean;
    cacheConversation?: boolean;
  }
  
  export interface ClientCertificateOptions {
    cert: string;
    key: string;
    passphrase?: string;
  }
  
  export interface StepWithParams {
    name: StepName;
    params: { [key: string]: any };
  }
  
  export interface ContextProviderWithParams {
    name: ContextProviderName;
    params: { [key: string]: any };
  }
  
  export interface SlashCommandDescription {
    name: string;
    description: string;
    params?: { [key: string]: any };
  }
  
  export interface CustomCommand {
    name: string;
    prompt: string;
    description: string;
  }
  
  interface Prediction {
    type: "content";
    content:
      | string
      | {
          type: "text";
          text: string;
        }[];
  }
  
  export interface ToolExtras {
    ide: IDE;
    llm: ILLM;
    fetch: FetchFunction;
  }
  
  export interface Tool {
    type: "function";
    function: {
      name: string;
      description?: string;
      parameters?: Record<string, any>;
      strict?: boolean | null;
    };
  
    displayTitle: string;
    wouldLikeTo: string;
    readonly: boolean;
    uri?: string;
  }
  
  interface BaseCompletionOptions {
    temperature?: number;
    topP?: number;
    topK?: number;
    minP?: number;
    presencePenalty?: number;
    frequencyPenalty?: number;
    mirostat?: number;
    stop?: string[];
    maxTokens?: number;
    numThreads?: number;
    useMmap?: boolean;
    keepAlive?: number;
    raw?: boolean;
    stream?: boolean;
    prediction?: Prediction;
    tools?: Tool[];
  }
  
  export interface ModelCapability {
    uploadImage?: boolean;
  }
  
  export interface ModelDescription {
    title: string;
    provider: string;
    model: string;
    apiKey?: string;
    apiBase?: string;
    contextLength?: number;
    maxStopWords?: number;
    template?: TemplateType;
    completionOptions?: BaseCompletionOptions;
    systemMessage?: string;
    requestOptions?: RequestOptions;
    promptTemplates?: { [key: string]: string };
    capabilities?: ModelCapability;
    cacheBehavior?: CacheBehavior;
  }
  
  export interface EmbedOptions {
    apiBase?: string;
    apiKey?: string;
    model?: string;
    deployment?: string;
    apiType?: string;
    apiVersion?: string;
    requestOptions?: RequestOptions;
    maxChunkSize?: number;
    maxBatchSize?: number;
    // AWS options
    profile?: string;
  
    // AWS and GCP Options
    region?: string;
  
    // GCP and Watsonx Options
    projectId?: string;
  }
  
  export interface EmbeddingsProviderDescription extends EmbedOptions {
    provider: string;
  }
  
  export interface RerankerDescription {
    name: string;
    params?: { [key: string]: any };
  }
  
  export interface TabAutocompleteOptions {
    disable: boolean;
    maxPromptTokens: number;
    debounceDelay: number;
    maxSuffixPercentage: number;
    prefixPercentage: number;
    transform?: boolean;
    template?: string;
    multilineCompletions: "always" | "never" | "auto";
    slidingWindowPrefixPercentage: number;
    slidingWindowSize: number;
    useCache: boolean;
    onlyMyCode: boolean;
    useRecentlyEdited: boolean;
    disableInFiles?: string[];
    useImports?: boolean;
    showWhateverWeHaveAtXMs?: number;
  }
  
  interface StdioOptions {
    type: "stdio";
    command: string;
    args: string[];
  }
  
  interface WebSocketOptions {
    type: "websocket";
    url: string;
  }
  
  interface SSEOptions {
    type: "sse";
    url: string;
  }
  
  type TransportOptions = StdioOptions | WebSocketOptions | SSEOptions;
  
  export interface MCPOptions {
    transport: TransportOptions;
  }
  
  export interface ContinueUIConfig {
    codeBlockToolbarPosition?: "top" | "bottom";
    fontSize?: number;
    displayRawMarkdown?: boolean;
    showChatScrollbar?: boolean;
    codeWrap?: boolean;
  }
  
  interface ContextMenuConfig {
    comment?: string;
    docstring?: string;
    fix?: string;
    optimize?: string;
    fixGrammar?: string;
  }
  
  interface ExperimentalModelRoles {
    inlineEdit?: string;
    applyCodeBlock?: string;
    repoMapFileSelection?: string;
  }
  
  export type EditStatus =
    | "not-started"
    | "streaming"
    | "accepting"
    | "accepting:full-diff"
    | "done";
  
  export type ApplyStateStatus =
    | "streaming" // Changes are being applied to the file
    | "done" // All changes have been applied, awaiting user to accept/reject
    | "closed"; // All changes have been applied. Note that for new files, we immediately set the status to "closed"
  
  export interface ApplyState {
    streamId: string;
    status?: ApplyStateStatus;
    numDiffs?: number;
    filepath?: string;
    fileContent?: string;
  }
  
  export interface RangeInFileWithContents {
    filepath: string;
    range: {
      start: { line: number; character: number };
      end: { line: number; character: number };
    };
    contents: string;
  }
  
  export type CodeToEdit = RangeInFileWithContents | FileWithContents;
  
  /**
   * Represents the configuration for a quick action in the Code Lens.
   * Quick actions are custom commands that can be added to function and class declarations.
   */
  interface QuickActionConfig {
    /**
     * The title of the quick action that will display in the Code Lens.
     */
    title: string;
  
    /**
     * The prompt that will be sent to the model when the quick action is invoked,
     * with the function or class body concatenated.
     */
    prompt: string;
  
    /**
     * If \`true\`, the result of the quick action will be sent to the chat panel.
     * If \`false\`, the streamed result will be inserted into the document.
     *
     * Defaults to \`false\`.
     */
    sendToChat: boolean;
  }
  
  export type DefaultContextProvider = ContextProviderWithParams & {
    query?: string;
  };
  
  interface ExperimentalConfig {
    contextMenuPrompts?: ContextMenuConfig;
    modelRoles?: ExperimentalModelRoles;
    defaultContext?: DefaultContextProvider[];
    promptPath?: string;
  
    /**
     * Quick actions are a way to add custom commands to the Code Lens of
     * function and class declarations.
     */
    quickActions?: QuickActionConfig[];
  
    /**
     * Automatically read LLM chat responses aloud using system TTS models
     */
    readResponseTTS?: boolean;
  
    /**
     * If set to true, we will attempt to pull down and install an instance of Chromium
     * that is compatible with the current version of Puppeteer.
     * This is needed to crawl a large number of documentation sites that are dynamically rendered.
     */
    useChromiumForDocsCrawling?: boolean;
    useTools?: boolean;
    modelContextProtocolServers?: MCPOptions[];
  }
  
  interface AnalyticsConfig {
    type: string;
    url?: string;
    clientKey?: string;
  }
  
  // config.json
  export interface SerializedContinueConfig {
    env?: string[];
    allowAnonymousTelemetry?: boolean;
    models: ModelDescription[];
    systemMessage?: string;
    completionOptions?: BaseCompletionOptions;
    requestOptions?: RequestOptions;
    slashCommands?: SlashCommandDescription[];
    customCommands?: CustomCommand[];
    contextProviders?: ContextProviderWithParams[];
    disableIndexing?: boolean;
    disableSessionTitles?: boolean;
    userToken?: string;
    embeddingsProvider?: EmbeddingsProviderDescription;
    tabAutocompleteModel?: ModelDescription | ModelDescription[];
    tabAutocompleteOptions?: Partial<TabAutocompleteOptions>;
    ui?: ContinueUIConfig;
    reranker?: RerankerDescription;
    experimental?: ExperimentalConfig;
    analytics?: AnalyticsConfig;
    docs?: SiteIndexingConfig[];
  }
  
  export type ConfigMergeType = "merge" | "overwrite";
  
  export type ContinueRcJson = Partial<SerializedContinueConfig> & {
    mergeBehavior: ConfigMergeType;
  };
  
  // config.ts - give users simplified interfaces
  export interface Config {
    /** If set to true, Continue will collect anonymous usage data to improve the product. If set to false, we will collect nothing. Read here to learn more: https://docs.continue.dev/telemetry */
    allowAnonymousTelemetry?: boolean;
    /** Each entry in this array will originally be a ModelDescription, the same object from your config.json, but you may add CustomLLMs.
     * A CustomLLM requires you only to define an AsyncGenerator that calls the LLM and yields string updates. You can choose to define either \`streamCompletion\` or \`streamChat\` (or both).
     * Continue will do the rest of the work to construct prompt templates, handle context items, prune context, etc.
     */
    models: (CustomLLM | ModelDescription)[];
    /** A system message to be followed by all of your models */
    systemMessage?: string;
    /** The default completion options for all models */
    completionOptions?: BaseCompletionOptions;
    /** Request options that will be applied to all models and context providers */
    requestOptions?: RequestOptions;
    /** The list of slash commands that will be available in the sidebar */
    slashCommands?: SlashCommand[];
    /** Each entry in this array will originally be a ContextProviderWithParams, the same object from your config.json, but you may add CustomContextProviders.
     * A CustomContextProvider requires you only to define a title and getContextItems function. When you type '@title <query>', Continue will call \`getContextItems(query)\`.
     */
    contextProviders?: (CustomContextProvider | ContextProviderWithParams)[];
    /** If set to true, Continue will not index your codebase for retrieval */
    disableIndexing?: boolean;
    /** If set to true, Continue will not make extra requests to the LLM to generate a summary title of each session. */
    disableSessionTitles?: boolean;
    /** An optional token to identify a user. Not used by Continue unless you write custom coniguration that requires such a token */
    userToken?: string;
    /** The provider used to calculate embeddings. If left empty, Continue will use transformers.js to calculate the embeddings with all-MiniLM-L6-v2 */
    embeddingsProvider?: EmbeddingsProviderDescription | ILLM;
    /** The model that Continue will use for tab autocompletions. */
    tabAutocompleteModel?:
      | CustomLLM
      | ModelDescription
      | (CustomLLM | ModelDescription)[];
    /** Options for tab autocomplete */
    tabAutocompleteOptions?: Partial<TabAutocompleteOptions>;
    /** UI styles customization */
    ui?: ContinueUIConfig;
    /** Options for the reranker */
    reranker?: RerankerDescription | ILLM;
    /** Experimental configuration */
    experimental?: ExperimentalConfig;
    /** Analytics configuration */
    analytics?: AnalyticsConfig;
  }
  
  // in the actual Continue source code
  export interface ContinueConfig {
    allowAnonymousTelemetry?: boolean;
    models: ILLM[];
    systemMessage?: string;
    completionOptions?: BaseCompletionOptions;
    requestOptions?: RequestOptions;
    slashCommands?: SlashCommand[];
    contextProviders?: IContextProvider[];
    disableSessionTitles?: boolean;
    disableIndexing?: boolean;
    userToken?: string;
    embeddingsProvider: ILLM;
    tabAutocompleteModels?: ILLM[];
    tabAutocompleteOptions?: Partial<TabAutocompleteOptions>;
    ui?: ContinueUIConfig;
    reranker?: ILLM;
    experimental?: ExperimentalConfig;
    analytics?: AnalyticsConfig;
    docs?: SiteIndexingConfig[];
    tools: Tool[];
  }
  
  export interface BrowserSerializedContinueConfig {
    allowAnonymousTelemetry?: boolean;
    models: ModelDescription[];
    systemMessage?: string;
    completionOptions?: BaseCompletionOptions;
    requestOptions?: RequestOptions;
    slashCommands?: SlashCommandDescription[];
    contextProviders?: ContextProviderDescription[];
    disableIndexing?: boolean;
    disableSessionTitles?: boolean;
    userToken?: string;
    embeddingsProvider?: string;
    ui?: ContinueUIConfig;
    reranker?: RerankerDescription;
    experimental?: ExperimentalConfig;
    analytics?: AnalyticsConfig;
    docs?: SiteIndexingConfig[];
    tools: Tool[];
  }
  
  // DOCS SUGGESTIONS AND PACKAGE INFO
  export interface FilePathAndName {
    path: string;
    name: string;
  }
  
  export interface PackageFilePathAndName extends FilePathAndName {
    packageRegistry: string; // e.g. npm, pypi
  }
  
  export type ParsedPackageInfo = {
    name: string;
    packageFile: PackageFilePathAndName;
    language: string;
    version: string;
  };
  
  export type PackageDetails = {
    docsLink?: string;
    docsLinkWarning?: string;
    title?: string;
    description?: string;
    repo?: string;
    license?: string;
  };
  
  export type PackageDetailsSuccess = PackageDetails & {
    docsLink: string;
  };
  
  export type PackageDocsResult = {
    packageInfo: ParsedPackageInfo;
  } & (
    | { error: string; details?: never }
    | { details: PackageDetailsSuccess; error?: never }
  );
}

export {};
`;

export default Types;
