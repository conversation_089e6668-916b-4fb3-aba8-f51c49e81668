---
title: Tools
description: Tool use and customization
keywords: [tool, use, function calling, claude, automatic]
---

import TabI<PERSON> from "@theme/TabItem";
import Tabs from "@theme/Tabs";

Tools allow Continue to take action in your IDE and beyond (when you give permission). Currently, they are only supported for the following providers:

- [Anthropic](./model-providers/top-level/anthropic.mdx) - see recommended models [here](https://docs.anthropic.com/en/docs/build-with-claude/tool-use#choosing-a-model).
- [Ollama](./model-providers/top-level/ollama.mdx) - see recommended models [here](https://ollama.com/search?c=tools).
- [OpenAI](./model-providers/top-level/openai.mdx)
- [Gemini](./model-providers/top-level/gemini.mdx)

To use tools, click on the icon in the input toolbar like below.

![tools](/img/tool-use-example.png)

To let you balance speed and safety, each tool can be set to 1 of 3 modes:

- `Automatic`: When the LLM requests to use the tool, Continue will automatically call it and send the response to the LLM.
- `Allowed`: When the LLM requests to use the tool, Continue will first give you the opportunity to "Cancel" or "Continue" with the tool.
- `Disabled`: The LLM will not know about or be able to use the tool.

### Custom tools

Currently custom tools can be configured using the [Model Context Protocol](https://modelcontextprotocol.io/introduction), a standard proposed by Anthropic to unify prompts, context, and tool use.

MCP Servers can be added to hub Assistants using `mcpServers` blocks. You can explore available MCP server blocks [here](https://hub.continue.dev/explore/mcp).

To set up your own MCP server, read the [MCP quickstart](https://modelcontextprotocol.io/quickstart) and then [create an `mcpServers` block](https://hub.continue.dev/new?type=block&blockType=mcpServers) or add the following to your [config file](../customize/deep-dives/configuration.md):

<Tabs groupId="config-example">
  <TabItem value="yaml" label="YAML">
  ```yaml title="config.yaml"
  mcpServers:
    - name: My MCP Server
      command: uvx
      args:
        - mcp-server-sqlite
        - --db-path
        - /Users/<USER>/test.db
  ```
  </TabItem>
  <TabItem value="json" label="JSON">
  ```json title="config.json"
  {
    "experimental": {
      "modelContextProtocolServers": [
        {
          "transport": {
            "type": "stdio",
            "command": "uvx",
            "args": ["mcp-server-sqlite", "--db-path", "/Users/<USER>/test.db"]
          }  
        }
      ]
    }
  }
  ```
  </TabItem>
</Tabs>
