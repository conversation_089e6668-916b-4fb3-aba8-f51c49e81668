name: Publish Preview Extension

on:
  release:
    types: [prereleased]

  workflow_dispatch:
    inputs:
      publish_build:
        description: "Whether or not to publish the built extension to the VS Code marketplace"
        required: true
        default: false

jobs:
  check_release_name:
    runs-on: ubuntu-latest
    outputs:
      should_run: ${{ steps.check.outputs.should_run }}
    steps:
      - id: check
        working-directory: .
        run: |
          if [[ "${{ github.event.release.tag_name }}" == v1.1.*-vscode ]]; then
            echo "should_run=true" >> $GITHUB_OUTPUT
          else
            echo "should_run=false" >> $GITHUB_OUTPUT
          fi

  build:
    needs: check_release_name
    if: needs.check_release_name.outputs.should_run == 'true' || github.event_name == 'workflow_dispatch'
    strategy:
      matrix:
        include:
          - os: windows-latest
            platform: win32
            arch: x64
            npm_config_arch: x64
          - os: windows-latest
            platform: win32
            arch: arm64
            npm_config_arch: arm
          - os: ubuntu-latest
            platform: linux
            arch: x64
            npm_config_arch: x64
          - os: ubuntu-latest
            platform: linux
            arch: arm64
            npm_config_arch: arm64
          - os: ubuntu-latest
            platform: linux
            arch: armhf
            npm_config_arch: arm
          - os: ubuntu-latest
            platform: alpine
            arch: x64
            npm_config_arch: x64
          - os: macos-13
            platform: darwin
            arch: x64
            npm_config_arch: x64
          - os: macos-13
            platform: darwin
            arch: arm64
            npm_config_arch: arm64
    runs-on: ${{ matrix.os }}
    steps:
      # 1. Check-out repository
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0 # This ensures all tags are fetched

      - name: Checkout tag
        run: git checkout ${GITHUB_REF#refs/tags/}

      # 2. Install npm dependencies
      - name: Use Node.js from .nvmrc
        uses: actions/setup-node@v4
        with:
          node-version-file: ".nvmrc"

      - name: Cache extension node_modules
        uses: actions/cache@v3
        with:
          path: extensions/vscode/node_modules
          key: ${{ runner.os }}-node-${{ hashFiles('extensions/vscode/package-lock.json') }}

      - name: Cache core node_modules
        uses: actions/cache@v3
        with:
          path: core/node_modules
          key: ${{ runner.os }}-node-${{ hashFiles('core/package-lock.json') }}

      - name: Cache gui node_modules
        uses: actions/cache@v3
        with:
          path: gui/node_modules
          key: ${{ runner.os }}-node-${{ hashFiles('gui/package-lock.json') }}

      - name: Install extension Dependencies
        run: |
          cd extensions/vscode
          npm ci
        env:
          # https://github.com/microsoft/vscode-ripgrep/issues/9#issuecomment-643965333
          GITHUB_TOKEN: ${{ secrets.CI_GITHUB_TOKEN }}

      - name: Install gui Dependencies
        run: |
          cd gui
          npm ci

      # npm ci doesn't end up capturing the @lancedb/... dep because it's not in the package-lock.json
      - name: Install Core Dependencies
        run: |
          cd core
          npm ci
          npm i vectordb

      # - name: Run core tests
      #   run: |
      #     cd core
      #     npm run test
      #   env:
      #     OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}

      # 2.5. Pre package
      - name: Set var for environment info
        shell: pwsh
        run: echo "target=${{ matrix.platform }}-${{ matrix.arch }}" >> $env:GITHUB_ENV

      - name: Prepackage the extension
        run: |
          cd extensions/vscode
          npm run prepackage -- --target ${{ env.target }}

      # 3. Re-install esbuild (for cases that we force installed for another arch in prepackage.js)
      - name: Re-install esbuild
        run: |
          cd extensions/vscode
          npm install -f esbuild

      # 4. Run tests for the extension
      # - name: Install Xvfb for Linux and run tests
      #   run: |
      #     sudo apt-get install -y xvfb # Install Xvfb
      #     Xvfb :99 & # Start Xvfb
      #     export DISPLAY=:99 # Export the display number to the environment
      #     cd extensions/vscode
      #     npm run test
      #   if: matrix.os == 'ubuntu-latest'

      # - name: Run extension tests
      #   run: |
      #     cd extensions/vscode
      #     npm run test
      #   if: matrix.os != 'ubuntu-latest'

      # 5. Package the extension
      - name: Package the extension
        run: cd extensions/vscode && npx vsce package --pre-release --no-dependencies --target ${{ env.target }}

      # 6. Upload the .vsix as an artifact
      - uses: actions/upload-artifact@v4
        with:
          name: ${{ env.target }}-vsix
          path: "extensions/vscode/*.vsix"

  release:
    permissions:
      contents: write
    runs-on: ubuntu-latest
    needs:
      - build
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Set up Git
        run: |
          git config --local user.email "<EMAIL>"
          git config --local user.name "GitHub Action"

      # Download the .vsix artifacts
      - uses: actions/download-artifact@v4
        with:
          pattern: "*-vsix"
          path: vsix-artifacts
          merge-multiple: true

      - name: Release
        uses: softprops/action-gh-release@v2
        with:
          tag_name: ${{ github.ref_name }}
          files: |
            vsix-artifacts/*.vsix
          token: ${{ secrets.CI_GITHUB_TOKEN }}
          repository: continuedev/continue
          prerelease: true

  publish:
    runs-on: ubuntu-latest
    needs:
      - build
    if: github.event_name != 'workflow_dispatch' || github.event.inputs.publish_build == 'true'
    permissions:
      contents: write
    steps:
      # 0. Setup git
      - name: Checkout
        uses: actions/checkout@v4

      - name: Set up Git
        run: |
          git config --local user.email "<EMAIL>"
          git config --local user.name "GitHub Action"

      - name: Pull latest changes from release
        run: git fetch origin ${{ github.ref }} && git checkout ${{ github.ref }}

      # 1. Download the artifacts
      - uses: actions/download-artifact@v4
        with:
          pattern: "*-vsix"
          path: vsix-artifacts
          merge-multiple: true

      # 2. Publish the extension to VS Code Marketplace
      - name: Publish to VS Code Marketplace
        run: |
          cd extensions/vscode
          npx vsce publish --pre-release --packagePath ../../vsix-artifacts/*.vsix
        env:
          VSCE_PAT: ${{ secrets.VSCE_TOKEN }}

      # 3. Publish the extension to Open VSX Registry
      - name: Publish (Open VSX Registry)
        continue-on-error: true
        run: |
          cd extensions/vscode
          npx ovsx publish --pre-release -p ${{ secrets.VSX_REGISTRY_TOKEN }} --packagePath ../../vsix-artifacts/*.vsix

      # 4. Update the package.json version and push changes
      # - name: Update version in package.json
      #   run: |
      #     cd extensions/vscode
      #     npm version patch

      # - name: Commit changes
      #   run: |
      #     git config --local user.email "<EMAIL>"
      #     git config --local user.name "GitHub Action"
      #     git commit -am "💚 Update package.json version [skip ci]"

      # - name: Push changes
      #   uses: ad-m/github-push-action@master
      #   with:
      #     github_token: ${{ secrets.GITHUB_TOKEN }}
      #     branch: ${{ github.ref }}
